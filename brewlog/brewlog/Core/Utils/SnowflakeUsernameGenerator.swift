import Foundation
#if canImport(UIKit)
import UIKit
#endif

/// Snowflake算法用户名生成器
/// 结合时间戳和咖啡相关词汇生成唯一且可读的用户名
class SnowflakeUsernameGenerator {
    
    // MARK: - 单例
    static let shared = SnowflakeUsernameGenerator()
    
    // MARK: - 私有属性
    private let epoch: Int64 = 1640995200000 // 2022-01-01 00:00:00 UTC 的毫秒时间戳
    private let machineId: Int64
    private var sequence: Int64 = 0
    private var lastTimestamp: Int64 = -1
    
    // 咖啡相关词汇库
    private let coffeeTerms = [
        "brew", "roast", "grind", "pour", "drip", "shot", "crema", "bloom",
        "v60", "aero", "chemex", "kalita", "hario", "bean", "espresso",
        "latte", "mocha", "cappuccino", "americano", "macchiato", "cortado",
        "flat", "french", "cold", "iced", "hot", "steam", "milk", "foam",
        "berry", "citrus", "choco", "caramel", "floral", "nutty", "fruity",
        "sweet", "bitter", "sour", "acid", "body", "aroma", "flavor",
        "origin", "single", "blend", "dark", "light", "medium", "filter",
        "press", "siphon", "moka", "turkish", "ibrik", "clever", "wave"
    ]
    
    private let adjectives = [
        "smooth", "rich", "bold", "mild", "strong", "gentle", "bright",
        "deep", "pure", "fresh", "warm", "cool", "fine", "premium",
        "special", "classic", "modern", "vintage", "artisan", "craft"
    ]
    
    // MARK: - 初始化
    private init() {
        // 使用设备标识符生成机器ID
        #if canImport(UIKit)
        let deviceId = UIDevice.current.identifierForVendor?.uuidString ?? "default"
        #else
        let deviceId = "default"
        #endif
        self.machineId = Int64(abs(deviceId.hashValue) % 1024) // 限制在10位以内
    }
    
    // MARK: - 公共方法
    
    /// 生成唯一的用户名
    /// - Returns: 符合规则的用户名（5+字符，字母开头，只包含字母数字）
    func generateUsername() -> String {
        let snowflakeId = generateSnowflakeId()
        return createReadableUsername(from: snowflakeId)
    }
    
    /// 批量生成用户名
    /// - Parameter count: 生成数量
    /// - Returns: 用户名数组
    func generateUsernames(count: Int) -> [String] {
        var usernames: [String] = []
        for _ in 0..<count {
            usernames.append(generateUsername())
        }
        return usernames
    }
    
    // MARK: - 私有方法
    
    /// 生成Snowflake ID
    private func generateSnowflakeId() -> Int64 {
        var timestamp = currentTimeMillis()
        
        if timestamp < lastTimestamp {
            // 时钟回拨，等待到下一毫秒
            timestamp = waitNextMillis(lastTimestamp)
        }
        
        if timestamp == lastTimestamp {
            // 同一毫秒内，序列号递增
            sequence = (sequence + 1) & 4095 // 12位序列号，最大4095
            if sequence == 0 {
                // 序列号溢出，等待下一毫秒
                timestamp = waitNextMillis(lastTimestamp)
            }
        } else {
            // 新的毫秒，序列号重置
            sequence = 0
        }
        
        lastTimestamp = timestamp
        
        // 组装Snowflake ID
        // 41位时间戳 + 10位机器ID + 12位序列号 + 1位符号位(0)
        let snowflakeId = ((timestamp - epoch) << 22) | (machineId << 12) | sequence
        
        return snowflakeId
    }
    
    /// 从Snowflake ID创建可读用户名
    private func createReadableUsername(from snowflakeId: Int64) -> String {
        // 使用Snowflake ID的不同部分来选择词汇
        let termIndex = Int(abs(snowflakeId) % Int64(coffeeTerms.count))
        let adjIndex = Int(abs(snowflakeId >> 16) % Int64(adjectives.count))
        let numberPart = abs(snowflakeId) % 9999 // 取4位数字
        
        let baseTerm = coffeeTerms[termIndex]
        let adjective = adjectives[adjIndex]
        
        // 生成不同的组合模式
        let patterns = [
            "\(baseTerm)\(numberPart)",
            "\(adjective)\(baseTerm)",
            "\(baseTerm)\(adjective)",
            "\(adjective)\(numberPart)",
            "\(baseTerm)\(String(numberPart).suffix(2))",
            "\(adjective)\(baseTerm)\(String(numberPart).suffix(2))"
        ]
        
        // 选择一个符合长度要求的模式
        let patternIndex = Int(abs(snowflakeId >> 32) % Int64(patterns.count))
        var username = patterns[patternIndex]
        
        // 确保用户名符合规则
        username = sanitizeUsername(username)
        
        // 如果长度不够，添加数字后缀
        if username.count < 5 {
            let suffix = String(numberPart).suffix(5 - username.count)
            username += suffix
        }
        
        // 如果长度过长，截断
        if username.count > 20 {
            username = String(username.prefix(20))
        }
        
        return username
    }
    
    /// 清理用户名，确保符合规则
    private func sanitizeUsername(_ username: String) -> String {
        var result = ""
        
        for char in username {
            if char.isLetter || char.isNumber {
                result.append(char)
            }
        }
        
        // 确保以字母开头
        if let firstChar = result.first, firstChar.isNumber {
            // 如果第一个字符是数字，在前面添加一个字母
            let letters = "abcdefghijklmnopqrstuvwxyz"
            let randomLetter = letters.randomElement()!
            result = String(randomLetter) + result
        }
        
        // 如果结果为空或只有数字，使用默认前缀
        if result.isEmpty || result.allSatisfy({ $0.isNumber }) {
            result = "brew" + result
        }
        
        return result.lowercased()
    }
    
    /// 获取当前时间戳（毫秒）
    private func currentTimeMillis() -> Int64 {
        return Int64(Date().timeIntervalSince1970 * 1000)
    }
    
    /// 等待到下一毫秒
    private func waitNextMillis(_ lastTimestamp: Int64) -> Int64 {
        var timestamp = currentTimeMillis()
        while timestamp <= lastTimestamp {
            timestamp = currentTimeMillis()
        }
        return timestamp
    }
}

// MARK: - 扩展方法
extension SnowflakeUsernameGenerator {
    
    /// 验证生成的用户名是否符合规则
    /// - Parameter username: 用户名
    /// - Returns: 是否有效
    func isValidUsername(_ username: String) -> Bool {
        // 检查长度（至少5个字符）
        guard username.count >= 5 else { return false }
        
        // 检查字符（只允许字母和数字）
        let allowedCharacters = CharacterSet.alphanumerics
        let usernameCharacterSet = CharacterSet(charactersIn: username)
        guard allowedCharacters.isSuperset(of: usernameCharacterSet) else { return false }
        
        // 检查是否以字母开头
        guard let firstChar = username.first, firstChar.isLetter else { return false }
        
        return true
    }
    
    /// 生成并验证用户名，确保符合规则
    /// - Returns: 有效的用户名
    func generateValidUsername() -> String {
        var username = generateUsername()
        var attempts = 0
        
        // 最多尝试10次，确保生成有效用户名
        while !isValidUsername(username) && attempts < 10 {
            username = generateUsername()
            attempts += 1
        }
        
        // 如果仍然无效，使用备用方案
        if !isValidUsername(username) {
            let timestamp = currentTimeMillis()
            username = "brew\(timestamp % 99999)"
        }
        
        return username
    }
}
