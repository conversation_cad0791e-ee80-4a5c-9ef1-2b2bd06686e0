import Foundation
#if canImport(UIKit)
import UIKit
#endif

/// Snowflake算法用户名生成器
/// 以唯一性为首要原则，结合咖啡相关词汇生成唯一且可读的用户名
class SnowflakeUsernameGenerator {

    // MARK: - 单例
    static let shared = SnowflakeUsernameGenerator()

    // MARK: - 私有属性
    private let epoch: Int64 = 1640995200000 // 2022-01-01 00:00:00 UTC 的毫秒时间戳
    private let machineId: Int64
    private var sequence: Int64 = 0
    private var lastTimestamp: Int64 = -1

    // 扩大的咖啡相关词汇库
    private let coffeeTerms = [
        // 冲煮方法
        "brew", "pour", "drip", "press", "siphon", "aero", "chemex", "v60", "kalita", "hario",
        "clever", "wave", "origami", "trinity", "april", "timemore", "fellow", "comandante",
        "baratza", "wilfa", "eureka", "mazzer", "mahlkonig", "ek43", "forte", "vario",

        // 咖啡类型
        "espresso", "americano", "latte", "cappuccino", "macchiato", "cortado", "gibraltar",
        "flat", "mocha", "affogato", "ristretto", "lungo", "doppio", "romano", "breve",
        "con", "panna", "bombom", "cafe", "noisette", "piccolo", "magic", "dirty",

        // 咖啡豆相关
        "bean", "roast", "grind", "single", "blend", "origin", "estate", "micro", "lot",
        "peaberry", "honey", "natural", "washed", "semi", "anaerobic", "carbonic", "extended",

        // 风味描述
        "berry", "citrus", "stone", "tropical", "floral", "herbal", "spice", "nutty",
        "choco", "caramel", "vanilla", "honey", "maple", "brown", "sugar", "molasses",
        "fruity", "wine", "grape", "apple", "pear", "peach", "apricot", "cherry",
        "orange", "lemon", "lime", "grapefruit", "blackberry", "blueberry", "strawberry",
        "raspberry", "cranberry", "raisin", "fig", "date", "prune", "coconut", "almond",
        "hazelnut", "walnut", "pecan", "peanut", "cashew", "pistachio", "sesame",

        // 产地相关
        "ethiopia", "kenya", "rwanda", "burundi", "tanzania", "uganda", "yemen", "hawaii",
        "jamaica", "colombia", "brazil", "peru", "bolivia", "ecuador", "venezuela",
        "guatemala", "honduras", "nicaragua", "costa", "rica", "panama", "mexico",
        "java", "sumatra", "sulawesi", "flores", "bali", "timor", "papua", "india",
        "vietnam", "laos", "thailand", "myanmar", "china", "yunnan", "taiwan",

        // 处理法
        "washed", "natural", "honey", "pulped", "semi", "wet", "dry", "anaerobic",
        "carbonic", "maceration", "extended", "fermentation", "black", "red", "yellow",
        "white", "orange", "pink", "purple", "mosaic", "thermal", "shock"
    ]

    private let adjectives = [
        // 基础形容词
        "smooth", "rich", "bold", "mild", "strong", "gentle", "bright", "deep",
        "pure", "fresh", "warm", "cool", "fine", "premium", "special", "classic",
        "modern", "vintage", "artisan", "craft", "select", "reserve", "limited",
        "exclusive", "rare", "unique", "exotic", "supreme", "ultimate", "perfect",

        // 风味形容词
        "sweet", "bitter", "sour", "acid", "balanced", "complex", "layered", "nuanced",
        "intense", "delicate", "subtle", "pronounced", "vibrant", "lively", "crisp",
        "clean", "clear", "muddy", "heavy", "light", "medium", "full", "thin",
        "thick", "syrupy", "juicy", "dry", "wet", "creamy", "silky", "velvety",

        // 烘焙相关
        "light", "medium", "dark", "blonde", "cinnamon", "city", "full", "french",
        "italian", "espresso", "nordic", "scandinavian", "third", "wave", "specialty",
        "commercial", "commodity", "grade", "screen", "density", "moisture"
    ]

    // 新增：Base64安全字符集（用于编码Snowflake ID）
    private let base64SafeChars = "abcdefghijklmnopqrstuvwxyz0123456789"
    
    // MARK: - 初始化
    private init() {
        // 改进的机器ID生成机制
        self.machineId = Self.generateMachineId()
    }

    /// 生成机器ID的静态方法
    private static func generateMachineId() -> Int64 {
        // 优先级：环境变量 > 设备标识符 > 随机值

        // 1. 尝试从环境变量获取机器ID
        if let envMachineId = ProcessInfo.processInfo.environment["BREWLOG_MACHINE_ID"],
           let machineId = Int64(envMachineId) {
            return abs(machineId) % 1024 // 限制在10位以内
        }

        // 2. 使用设备标识符
        #if canImport(UIKit)
        if let deviceId = UIDevice.current.identifierForVendor?.uuidString {
            // 使用设备ID的哈希值，但增加更多熵
            let bundleId = Bundle.main.bundleIdentifier ?? ""
            let combined = deviceId + bundleId
            return Int64(abs(combined.hashValue) % 1024)
        }
        #endif

        // 3. 使用应用Bundle标识符作为备选
        if let bundleId = Bundle.main.bundleIdentifier {
            return Int64(abs(bundleId.hashValue) % 1024)
        }

        // 4. 最后的备选方案：基于当前时间的伪随机值
        let timestamp = Date().timeIntervalSince1970
        return Int64(abs(timestamp.hashValue) % 1024)
    }
    
    // MARK: - 公共方法

    /// 生成唯一的用户名（同步版本，用于本地生成）
    /// - Returns: 符合规则的用户名（5+字符，字母开头，只包含字母数字）
    func generateUsername() -> String {
        let snowflakeId = generateSnowflakeId()
        return createReadableUsername(from: snowflakeId)
    }

    /// 生成唯一的用户名（异步版本，包含服务端唯一性检查）
    /// - Parameter maxRetries: 最大重试次数
    /// - Returns: 经过服务端验证的唯一用户名
    func generateUniqueUsername(maxRetries: Int = 10) async throws -> String {
        var attempts = 0

        while attempts < maxRetries {
            let username = generateUsername()

            do {
                // 检查用户名是否可用
                let response = try await APIService.shared.checkUsernameAvailability(username: username)

                if response.available {
                    print("✅ 生成经过服务端验证的唯一用户名: \(username)")
                    return username
                } else {
                    attempts += 1
                    print("🔄 用户名 '\(username)' 已被占用，重试第 \(attempts) 次")

                    // 如果用户名被占用，强制生成新的Snowflake ID
                    try await Task.sleep(nanoseconds: 1_000_000) // 等待1毫秒确保时间戳不同
                }
            } catch {
                print("❌ 检查用户名可用性失败: \(error)")
                // 如果API调用失败，返回本地生成的用户名
                return username
            }
        }

        // 如果达到最大重试次数，返回最后生成的用户名
        print("⚠️ 达到最大重试次数，返回本地生成的用户名")
        return generateUsername()
    }

    /// 批量生成用户名
    /// - Parameter count: 生成数量
    /// - Returns: 用户名数组
    func generateUsernames(count: Int) -> [String] {
        var usernames: [String] = []
        for _ in 0..<count {
            usernames.append(generateUsername())
        }
        return usernames
    }
    
    // MARK: - 私有方法
    
    /// 生成Snowflake ID
    private func generateSnowflakeId() -> Int64 {
        var timestamp = currentTimeMillis()
        
        if timestamp < lastTimestamp {
            // 时钟回拨，等待到下一毫秒
            timestamp = waitNextMillis(lastTimestamp)
        }
        
        if timestamp == lastTimestamp {
            // 同一毫秒内，序列号递增
            sequence = (sequence + 1) & 4095 // 12位序列号，最大4095
            if sequence == 0 {
                // 序列号溢出，等待下一毫秒
                timestamp = waitNextMillis(lastTimestamp)
            }
        } else {
            // 新的毫秒，序列号重置
            sequence = 0
        }
        
        lastTimestamp = timestamp
        
        // 组装Snowflake ID
        // 41位时间戳 + 10位机器ID + 12位序列号 + 1位符号位(0)
        let snowflakeId = ((timestamp - epoch) << 22) | (machineId << 12) | sequence
        
        return snowflakeId
    }
    
    /// 从Snowflake ID创建可读用户名（重新设计，以唯一性为首要原则）
    private func createReadableUsername(from snowflakeId: Int64) -> String {
        // 将Snowflake ID分解为不同的部分，确保每个部分都参与用户名生成
        let timestampPart = (snowflakeId >> 22) & 0x1FFFFFFFFFF // 41位时间戳
        let machinePart = (snowflakeId >> 12) & 0x3FF           // 10位机器ID
        let sequencePart = snowflakeId & 0xFFF                  // 12位序列号

        // 使用时间戳部分选择词汇
        let termIndex = Int(abs(timestampPart) % Int64(coffeeTerms.count))
        let adjIndex = Int(abs(timestampPart >> 8) % Int64(adjectives.count))

        // 使用机器ID和序列号生成唯一后缀
        let uniqueSuffix = encodeToBase36(machinePart << 12 | sequencePart)

        let baseTerm = coffeeTerms[termIndex]
        let adjective = adjectives[adjIndex]

        // 根据时间戳的不同位选择组合模式
        let patternSelector = Int(abs(timestampPart >> 16) % 8)

        var username: String

        switch patternSelector {
        case 0:
            // 形容词 + 词汇 + 唯一后缀
            username = "\(adjective)\(baseTerm)\(uniqueSuffix)"
        case 1:
            // 词汇 + 唯一后缀
            username = "\(baseTerm)\(uniqueSuffix)"
        case 2:
            // 形容词 + 唯一后缀
            username = "\(adjective)\(uniqueSuffix)"
        case 3:
            // 词汇 + 形容词的前3个字符 + 唯一后缀
            let adjPrefix = String(adjective.prefix(3))
            username = "\(baseTerm)\(adjPrefix)\(uniqueSuffix)"
        case 4:
            // 形容词的前3个字符 + 词汇 + 唯一后缀
            let adjPrefix = String(adjective.prefix(3))
            username = "\(adjPrefix)\(baseTerm)\(uniqueSuffix)"
        case 5:
            // 词汇的前4个字符 + 形容词的前3个字符 + 唯一后缀
            let termPrefix = String(baseTerm.prefix(4))
            let adjPrefix = String(adjective.prefix(3))
            username = "\(termPrefix)\(adjPrefix)\(uniqueSuffix)"
        case 6:
            // 两个词汇组合 + 唯一后缀
            let secondTermIndex = Int(abs(timestampPart >> 24) % Int64(coffeeTerms.count))
            let secondTerm = coffeeTerms[secondTermIndex]
            let term1Prefix = String(baseTerm.prefix(3))
            let term2Prefix = String(secondTerm.prefix(3))
            username = "\(term1Prefix)\(term2Prefix)\(uniqueSuffix)"
        default:
            // 默认：形容词前缀 + 词汇前缀 + 完整唯一后缀
            let termPrefix = String(baseTerm.prefix(4))
            let adjPrefix = String(adjective.prefix(2))
            username = "\(adjPrefix)\(termPrefix)\(uniqueSuffix)"
        }

        // 确保用户名符合规则
        username = sanitizeUsername(username)

        // 如果长度不够5个字符，添加更多唯一标识
        if username.count < 5 {
            let additionalSuffix = encodeToBase36(abs(snowflakeId) % 999999)
            username += additionalSuffix
        }

        // 如果长度过长，智能截断但保留唯一性
        if username.count > 20 {
            // 保留前缀和后缀，确保唯一性
            let prefixLength = min(12, username.count - uniqueSuffix.count)
            let prefix = String(username.prefix(prefixLength))
            username = prefix + uniqueSuffix
        }

        return username
    }

    /// 将数字编码为Base36字符串（使用小写字母和数字）
    private func encodeToBase36(_ number: Int64) -> String {
        let base36Chars = "0123456789abcdefghijklmnopqrstuvwxyz"
        var result = ""
        var num = abs(number)

        if num == 0 {
            return "0"
        }

        while num > 0 {
            let remainder = Int(num % 36)
            result = String(base36Chars[base36Chars.index(base36Chars.startIndex, offsetBy: remainder)]) + result
            num /= 36
        }

        return result
    }
    
    /// 清理用户名，确保符合规则
    private func sanitizeUsername(_ username: String) -> String {
        var result = ""
        
        for char in username {
            if char.isLetter || char.isNumber {
                result.append(char)
            }
        }
        
        // 确保以字母开头
        if let firstChar = result.first, firstChar.isNumber {
            // 如果第一个字符是数字，在前面添加一个字母
            let letters = "abcdefghijklmnopqrstuvwxyz"
            let randomLetter = letters.randomElement()!
            result = String(randomLetter) + result
        }
        
        // 如果结果为空或只有数字，使用默认前缀
        if result.isEmpty || result.allSatisfy({ $0.isNumber }) {
            result = "brew" + result
        }
        
        return result.lowercased()
    }
    
    /// 获取当前时间戳（毫秒）
    private func currentTimeMillis() -> Int64 {
        return Int64(Date().timeIntervalSince1970 * 1000)
    }
    
    /// 等待到下一毫秒
    private func waitNextMillis(_ lastTimestamp: Int64) -> Int64 {
        var timestamp = currentTimeMillis()
        while timestamp <= lastTimestamp {
            timestamp = currentTimeMillis()
        }
        return timestamp
    }
}

// MARK: - 扩展方法
extension SnowflakeUsernameGenerator {
    
    /// 验证生成的用户名是否符合规则
    /// - Parameter username: 用户名
    /// - Returns: 是否有效
    func isValidUsername(_ username: String) -> Bool {
        // 检查长度（至少5个字符）
        guard username.count >= 5 else { return false }
        
        // 检查字符（只允许字母和数字）
        let allowedCharacters = CharacterSet.alphanumerics
        let usernameCharacterSet = CharacterSet(charactersIn: username)
        guard allowedCharacters.isSuperset(of: usernameCharacterSet) else { return false }
        
        // 检查是否以字母开头
        guard let firstChar = username.first, firstChar.isLetter else { return false }
        
        return true
    }
    
    /// 生成并验证用户名，确保符合规则（改进版本，更注重唯一性）
    /// - Returns: 有效的用户名
    func generateValidUsername() -> String {
        var username = generateUsername()
        var attempts = 0

        // 最多尝试10次，确保生成有效用户名
        while !isValidUsername(username) && attempts < 10 {
            // 强制等待1毫秒，确保时间戳不同
            Thread.sleep(forTimeInterval: 0.001)
            username = generateUsername()
            attempts += 1
        }

        // 如果仍然无效，使用备用方案（包含更多唯一性信息）
        if !isValidUsername(username) {
            let timestamp = currentTimeMillis()
            let machineInfo = encodeToBase36(machineId)
            username = "brew\(machineInfo)\(encodeToBase36(timestamp % 999999))"
        }

        return username
    }

    /// 生成多个候选用户名（用于提供选择）
    /// - Parameter count: 生成数量
    /// - Returns: 候选用户名数组
    func generateCandidateUsernames(count: Int = 3) -> [String] {
        var candidates: [String] = []
        var attempts = 0
        let maxAttempts = count * 3 // 允许更多尝试以确保多样性

        while candidates.count < count && attempts < maxAttempts {
            // 每次生成前等待一小段时间，确保时间戳不同
            if attempts > 0 {
                Thread.sleep(forTimeInterval: 0.001)
            }

            let username = generateValidUsername()

            // 确保不重复
            if !candidates.contains(username) {
                candidates.append(username)
            }

            attempts += 1
        }

        // 如果生成的候选数量不够，用备用方案补充
        while candidates.count < count {
            let timestamp = currentTimeMillis() + Int64(candidates.count)
            let machineInfo = encodeToBase36(machineId)
            let backup = "brew\(machineInfo)\(encodeToBase36(timestamp % 999999))"

            if !candidates.contains(backup) {
                candidates.append(backup)
            } else {
                // 如果备用方案也重复，添加额外的随机性
                let extra = encodeToBase36(Int64.random(in: 1000...9999))
                candidates.append("brew\(machineInfo)\(extra)")
            }
        }

        return candidates
    }
}
