import SwiftUI
#if canImport(UIKit)
import UIKit
#endif

struct RegisterView: View {
    @Environment(\.dismiss) private var dismiss
    @FocusState private var focusedField: String?

    @State private var username: String = ""
    @State private var email: String = ""
    @State private var password: String = ""
    @State private var confirmPassword: String = ""
    @State private var isLoading = false
    @State private var errorMessage: String?
    @State private var successMessage: String?
    @State private var showPasswordGenerator = false
    @State private var showPasswordVisibility = false
    @State private var generatedPasswords: [String] = []
    @State private var showOnboarding = false
    @State private var needsEmailVerification = false
    @State private var usernameCheckMessage: String?
    @State private var isCheckingUsername = false
    @State private var showUserAgreement = false
    @State private var showPrivacyPolicy = false

    var onRegisterSuccess: (() -> Void)?
    
    var body: some View {
        ZStack(alignment: .top) {
            // 固定的顶部导航栏
            topNavigationBar
                .background(Color.primaryBg)
                .zIndex(1)
            
            ScrollView {
                VStack(spacing: 8) {
                    // 添加一个占位符，高度等于导航栏高度
                    Color.clear
                        .frame(height: 52)
                    
                    // 主要内容区域
                    VStack(spacing: 16) {
                        // 基本信息区域
                        basicInfoSection
                        
                        // 密码设置区域
                        passwordSection
                        
                        // 成功信息区域
                        if let successMessage = successMessage {
                            successSection(successMessage)
                        }
                        
                        // 错误信息区域
                        if let errorMessage = errorMessage {
                            errorSection(errorMessage)
                        }
                        
                        // 操作按钮区域
                        actionButtonsSection
                    }
                    .padding(.horizontal, 12)
                    .padding(.bottom, 20)
                }
            }
            .background(
                Color.primaryBg
                    .onTapGesture {
                        hideKeyboard()
                    }
            )
        }
        .overlay(
            Group {
                if isLoading {
                    ZStack {
                        Color.black.opacity(0.4)
                        
                        BlinkingLoader(
                            color: .primaryBg,
                            width: 12,
                            height: 18,
                            duration: 1.5,
                            text: "注册中..."
                        )
                        .padding()
                        .background(Color.secondaryBg.opacity(0.9))
                        .cornerRadius(10)
                        .shadow(radius: 5)
                    }
                }
            }
        )
        .sheet(isPresented: $showOnboarding) {
            // OnboardingView() // 暂时注释，等待OnboardingView可用
            Text("欢迎使用！新手引导即将推出")
                .padding()
                .interactiveDismissDisabled(true)
        }
    }
    
    // 顶部导航栏
    private var topNavigationBar: some View {
        ZStack {
            HStack {
                Button(action: {
                    dismiss()
                }) {
                    Text("取消")
                        .foregroundColor(.linkText)
                }
                
                Spacer()
            }
        }
        .padding(.vertical, 12)
        .padding(.horizontal, 16)
        .background(Color.primaryBg)
    }
    
    // 基本信息区域
    private var basicInfoSection: some View {
        VStack(spacing: 16) {
            VStack(alignment: .leading, spacing: 8) {
                // 用户名输入
                VStack(alignment: .leading, spacing: 8) {
                    Text("注册用户名")
                        .font(.system(size: 16, weight: .regular))
                        .foregroundColor(.primaryText)
                    
                    HStack {
                        TextField("请输入用户名", text: $username)
                            .font(.system(size: 17))
                            .padding(.vertical, 14)
                            .padding(.horizontal, 16)
                            .background(Color.secondaryBg)
                            .cornerRadius(12)
                            .disableAutocorrection(true)
                            .autocapitalization(.none)
                            .submitLabel(.next)
                            .focused($focusedField, equals: "username_field")
                            .onChange(of: username) { _ in
                                clearError()
                                checkUsernameAvailability()
                            }
                        
                        Button(action: {
                            generateAndFillUsername()
                        }) {
                            Image(systemName: "wand.and.rays")
                                .foregroundColor(.functionText)
                                .padding(10)
                                .background(Color.secondaryBg)
                                .cornerRadius(8)
                        }
                        .buttonStyle(PlainButtonStyle())
                    }
                    
                    // 用户名检测结果
                    if let usernameCheckMessage = usernameCheckMessage {
                        HStack {
                            Image(systemName: usernameCheckMessage.contains("可用") ? "checkmark.circle.fill" : "xmark.circle.fill")
                                .foregroundColor(usernameCheckMessage.contains("可用") ? .green : .red)

                            Text(usernameCheckMessage)
                                .font(.caption)
                                .foregroundColor(usernameCheckMessage.contains("可用") ? .green : .red)
                        }
                    }
                    
                    if isCheckingUsername {
                        HStack {
                            ProgressView()
                                .scaleEffect(0.8)
                                .frame(width: 16, height: 16)
                            Text("检查用户名...")
                                .font(.caption)
                                .foregroundColor(.noteText)
                        }
                    }
                }
                

                
                // 邮箱输入
                VStack(alignment: .leading, spacing: 8) {
                    Text("注册邮箱")
                        .font(.system(size: 16, weight: .regular))
                        .foregroundColor(.primaryText)
                    
                    TextField("请输入邮箱", text: $email)
                        .font(.system(size: 17))
                        .padding(.vertical, 14)
                        .padding(.horizontal, 16)
                        .background(Color.secondaryBg)
                        .cornerRadius(12)
                        .disableAutocorrection(true)
                        .autocapitalization(.none)
                        .keyboardType(.emailAddress)
                        .submitLabel(.next)
                        .focused($focusedField, equals: "email_field")
                        .onChange(of: email) { _ in
                            clearError()
                        }
                }
                
                Text("用户名至少5个字符，只能包含字母和数字，必须以字母开头")
                    .font(.caption)
                    .foregroundColor(.noteText)
                    .fixedSize(horizontal: false, vertical: true)
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 16)
            .background(Color.primaryBg)
            .cornerRadius(10)
        }
    }
    
    // 密码设置区域
    private var passwordSection: some View {
        VStack(spacing: 16) {
            VStack(alignment: .leading, spacing: 8) {
                Text("密码设置")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.primaryText)
                
                // 密码控制栏
                HStack {
                    Text("密码可见性")
                        .font(.caption)
                        .foregroundColor(.secondaryText)
                    
                    Spacer()
                    
                    Button(action: {
                        showPasswordVisibility.toggle()
                    }) {
                        HStack {
                            Image(systemName: showPasswordVisibility ? "eye.slash" : "eye")
                            Text(showPasswordVisibility ? "隐藏" : "显示")
                        }
                        .font(.caption)
                        .foregroundColor(.functionText)
                    }
                    
                    Button(action: {
                        showPasswordGenerator.toggle()
                    }) {
                        HStack {
                            Image(systemName: "key.fill")
                            Text("生成密码")
                        }
                        .font(.caption)
                        .foregroundColor(.functionText)
                    }
                }
                .padding(.vertical, 4)
            }
            
            // 密码输入
            if showPasswordVisibility {
                TextField("密码", text: $password)
                    .font(.system(size: 17))
                    .padding(.vertical, 14)
                    .padding(.horizontal, 16)
                    .background(Color.secondaryBg)
                    .cornerRadius(12)
                    .disableAutocorrection(true)
                    .submitLabel(.next)
                    .focused($focusedField, equals: "password_field")
                    .onChange(of: password) { _ in
                        clearError()
                    }
            } else {
                SecureField("密码", text: $password)
                    .font(.system(size: 17))
                    .padding(.vertical, 14)
                    .padding(.horizontal, 16)
                    .background(Color.secondaryBg)
                    .cornerRadius(12)
                    .disableAutocorrection(true)
                    .submitLabel(.next)
                    .focused($focusedField, equals: "password_field")
                    .onChange(of: password) { _ in
                        clearError()
                    }
            }
            
            // 确认密码输入
            if showPasswordVisibility {
                TextField("确认密码", text: $confirmPassword)
                    .font(.system(size: 17))
                    .padding(.vertical, 14)
                    .padding(.horizontal, 16)
                    .background(Color.secondaryBg)
                    .cornerRadius(12)
                    .disableAutocorrection(true)
                    .submitLabel(.done)
                    .focused($focusedField, equals: "confirmPassword_field")
                    .onChange(of: confirmPassword) { _ in
                        clearError()
                    }
            } else {
                SecureField("确认密码", text: $confirmPassword)
                    .font(.system(size: 17))
                    .padding(.vertical, 14)
                    .padding(.horizontal, 16)
                    .background(Color.secondaryBg)
                    .cornerRadius(12)
                    .disableAutocorrection(true)
                    .submitLabel(.done)
                    .focused($focusedField, equals: "confirmPassword_field")
                    .onChange(of: confirmPassword) { _ in
                        clearError()
                    }
            }
            
            // 密码生成器
            if showPasswordGenerator {
                passwordGeneratorView
            }
            
            Text("密码至少8位，需包含字母和数字")
                .font(.caption)
                .foregroundColor(.noteText)
                .fixedSize(horizontal: false, vertical: true)
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 16)
        .background(Color.primaryBg)
        .cornerRadius(10)
    }
    
    // 成功信息区域
    private func successSection(_ message: String) -> some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Image(systemName: "checkmark.circle.fill")
                    .foregroundColor(.green)
                
                Text("注册成功")
                    .font(.headline)
                    .foregroundColor(.green)
            }
            
            Text(message)
                .foregroundColor(.primaryText)
                .fixedSize(horizontal: false, vertical: true)
            
            if needsEmailVerification {
                Text("请检查您的邮箱并点击验证链接以激活账户。")
                    .font(.caption)
                    .foregroundColor(.noteText)
                    .fixedSize(horizontal: false, vertical: true)
            }
        }
        .padding(.vertical, 4)
        .padding(.horizontal, 16)
        .background(Color.primaryBg)
        .cornerRadius(10)
    }
    
    // 错误信息区域
    private func errorSection(_ message: String) -> some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Image(systemName: "exclamationmark.triangle.fill")
                    .foregroundColor(.red)

                Text("注册失败")
                    .font(.headline)
                    .foregroundColor(.red)
            }

            Text(message)
                .foregroundColor(.red)
                .fixedSize(horizontal: false, vertical: true)
        }
        .padding(.vertical, 4)
        .padding(.horizontal, 16)
        .background(Color.primaryBg)
        .cornerRadius(10)
    }
    
    // 操作按钮区域
    private var actionButtonsSection: some View {
        VStack(spacing: 16) {
            // 注册按钮
            Button(action: register) {
                HStack {
                    if isLoading {
                        ProgressView()
                            .scaleEffect(0.8)
                            .frame(width: 16, height: 16)
                            .progressViewStyle(CircularProgressViewStyle(tint: .white))
                    } else {
                        Image("newAccount.symbols")
                    }
                    Text("注册")
                        .font(.system(size: 16, weight: .medium))
                }
                .foregroundColor(.white)
                .frame(maxWidth: .infinity)
                .padding(.vertical, 14)
                .background(isFormValid && !isLoading ? Color.functionText : Color.gray.opacity(0.5))
                .cornerRadius(12)
            }
            .buttonStyle(PlainButtonStyle())
            .disabled(isLoading || !isFormValid || successMessage != nil)
            .padding(.horizontal, 16)
            
            // 用户协议和隐私政策提示
            HStack(spacing: 0) {
                Text("注册即代表您已阅读并同意")
                    .font(.caption)
                    .foregroundColor(.noteText)
                
                Button(action: {
                    showUserAgreement = true
                }) {
                    Text(" 《用户协议》")
                        .font(.caption)
                        .foregroundColor(.functionText)
                }
                
                Button(action: {
                    showPrivacyPolicy = true
                }) {
                    Text(" 《隐私政策》")
                        .font(.caption)
                        .foregroundColor(.functionText)
                }
            }
            .padding(.horizontal, 16)
            
            // 开始使用按钮（注册成功后显示）
            if successMessage != nil {
                Button(action: {
                    showOnboarding = true
                }) {
                    HStack {
                        Image(systemName: "arrow.right.circle.fill")
                            .foregroundColor(.green)
                        Text("开始使用")
                            .foregroundColor(.green)
                    }
                    .font(.system(size: 16, weight: .medium))
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 14)
                    .background(Color.secondaryBg)
                    .cornerRadius(12)
                }
                .buttonStyle(PlainButtonStyle())
                .padding(.horizontal, 16)
            }
        }
        .padding(.vertical, 16)
        .background(Color.primaryBg)
        .cornerRadius(10)
        .sheet(isPresented: $showUserAgreement) {
            UserAgreementView()
                .presentationDragIndicator(.visible)
        }
        .sheet(isPresented: $showPrivacyPolicy) {
            PrivacyPolicyView()
                .presentationDragIndicator(.visible)
        }
    }
    
    // MARK: - 计算属性
    
    private var isFormValid: Bool {
        !username.isEmpty &&
        username.count >= 5 &&
        isValidUsername(username) &&
        !email.isEmpty &&
        !password.isEmpty &&
        !confirmPassword.isEmpty &&
        password == confirmPassword &&
        isValidEmail(email) &&
        password.count >= 8
    }
    
    // MARK: - 子视图



    private var passwordGeneratorView: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text("推荐密码")
                    .font(.caption)
                    .foregroundColor(.primaryText)

                Spacer()

                Button("生成新密码") {
                    generateNewPasswords()
                }
                .font(.caption)
                .foregroundColor(.functionText)
            }

            ForEach(Array(generatedPasswords.enumerated()), id: \.offset) { index, generatedPassword in
                passwordOptionView(password: generatedPassword, index: index)
            }

            Text("⚠️ 请妥善保管密码，即使忘记密码也可通过邮箱找回")
                .font(.caption)
                .foregroundColor(.noteText)
                .fixedSize(horizontal: false, vertical: true)
        }
        .padding(.vertical, 12)
        .padding(.horizontal, 12)
        .background(Color.secondaryBg.opacity(0.5))
        .cornerRadius(8)
        .onAppear {
            if generatedPasswords.isEmpty {
                generateNewPasswords()
            }
        }
    }
    


    private func passwordOptionView(password: String, index: Int) -> some View {
        HStack {
            Button(action: {
                selectPassword(password)
            }) {
                HStack {
                    Text(password)
                        .font(.system(.body, design: .monospaced))
                        .foregroundColor(.primaryText)

                    Spacer()
                }
            }
            .buttonStyle(PlainButtonStyle())

            Button(action: {
                copyPasswordToClipboard(password)
            }) {
                Image(systemName: "doc.on.doc")
                    .foregroundColor(.functionText)
            }
        }
        .padding(.vertical, 6)
    }
    
    // MARK: - 方法
    
    // 隐藏键盘方法
    private func hideKeyboard() {
        #if canImport(UIKit)
        UIApplication.shared.sendAction(#selector(UIResponder.resignFirstResponder), to: nil, from: nil, for: nil)
        #endif
    }
    
    private func register() {
        guard isFormValid else {
            errorMessage = "请检查输入信息"
            return
        }

        isLoading = true
        errorMessage = nil
        hideKeyboard()

        // 模拟注册过程
        DispatchQueue.main.asyncAfter(deadline: .now() + 2) {
            isLoading = false
            successMessage = "注册成功！欢迎加入咖啡爱好者社区"
            needsEmailVerification = true

            // 注册成功后不立即关闭，等待用户点击"开始使用"
        }

        // 实际的API调用（暂时注释）
        /*
        Task {
            do {
                // 调用APIService的注册方法，移除firstName参数
                let response = try await APIService.shared.register(
                    username: username,
                    email: email,
                    password: password
                )

                await MainActor.run {
                    isLoading = false
                    successMessage = "注册成功！欢迎加入咖啡爱好者社区"
                    needsEmailVerification = true

                    // 注册成功后不立即关闭，等待用户点击"开始使用"
                }
            } catch {
                await MainActor.run {
                    isLoading = false
                    errorMessage = "注册失败: \(error.localizedDescription)"
                }
            }
        }
        */
    }
    
    private func clearError() {
        errorMessage = nil
        successMessage = nil
    }

    private func checkUsernameAvailability() {
        guard !username.isEmpty && username.count >= 5 && isValidUsername(username) else {
            usernameCheckMessage = nil
            return
        }

        isCheckingUsername = true
        usernameCheckMessage = nil

        // 使用真实的API调用检测用户名可用性
        Task {
            do {
                let response = try await APIService.shared.checkUsernameAvailability(username: username)

                await MainActor.run {
                    isCheckingUsername = false
                    if response.available {
                        usernameCheckMessage = "用户名可用"
                    } else {
                        usernameCheckMessage = response.message
                    }
                }
            } catch {
                await MainActor.run {
                    isCheckingUsername = false
                    // 如果API调用失败，回退到模拟检测
                    let unavailableUsernames = ["admin", "test", "user", "coffee", "brew", "admin123", "test123"]

                    if unavailableUsernames.contains(username.lowercased()) {
                        usernameCheckMessage = "用户名已被占用"
                    } else {
                        usernameCheckMessage = "用户名可用"
                    }
                }
            }
        }
    }

    // 使用改进的Snowflake算法生成并直接填写用户名
    private func generateAndFillUsername() {
        // 使用新的生成器，优先保证唯一性
        let generatedUsername = SnowflakeUsernameGenerator.shared.generateValidUsername()
        username = generatedUsername

        print("🎲 生成新用户名: \(generatedUsername)")

        // 生成后立即检查可用性
        checkUsernameAvailability()
    }

    // 异步生成唯一用户名（包含服务端验证）
    private func generateUniqueUsernameAsync() {
        Task {
            do {
                let uniqueUsername = try await SnowflakeUsernameGenerator.shared.generateUniqueUsername()

                await MainActor.run {
                    username = uniqueUsername
                    print("✅ 生成经过服务端验证的唯一用户名: \(uniqueUsername)")

                    // 由于已经通过服务端验证，直接设置为可用
                    usernameCheckMessage = "用户名可用"
                }
            } catch {
                await MainActor.run {
                    print("❌ 生成唯一用户名失败，回退到本地生成: \(error)")
                    generateAndFillUsername()
                }
            }
        }
    }

    private func generateNewPasswords() {
        // 复杂密码生成
        generatedPasswords = generateComplexPasswords(count: 3)
    }

    private func generateComplexPasswords(count: Int) -> [String] {
        var passwords: [String] = []

        let uppercase = "ABCDEFGHIJKLMNOPQRSTUVWXYZ"
        let numbers = "0123456789"
        let symbols = "!@#$%^&*()_+-=[]{}|;:,.<>?"

        let coffeeWords = ["Coffee", "Brew", "Espresso", "Latte", "Mocha", "Cappuccino", "Americano", "Macchiato", "Cortado", "Flat", "Pour", "Drip", "French", "Aero", "Chemex", "V60", "Kalita", "Hario", "Bean", "Roast", "Grind", "Shot", "Crema", "Bloom", "Extract"]

        for _ in 0..<count {
            var password = ""

            // 随机选择一个咖啡词汇作为基础
            let baseWord = coffeeWords.randomElement()!
            password += baseWord

            // 添加随机数字 (2-3位)
            let numberCount = Int.random(in: 2...3)
            for _ in 0..<numberCount {
                password += String(numbers.randomElement()!)
            }

            // 添加随机符号 (1-2个)
            let symbolCount = Int.random(in: 1...2)
            for _ in 0..<symbolCount {
                password += String(symbols.randomElement()!)
            }

            // 随机插入大写字母
            if Bool.random() {
                let randomIndex = password.index(password.startIndex, offsetBy: Int.random(in: 1..<password.count))
                let randomUppercase = String(uppercase.randomElement()!)
                password.insert(contentsOf: randomUppercase, at: randomIndex)
            }

            // 确保密码长度在8-16之间
            if password.count >= 8 && password.count <= 16 {
                passwords.append(password)
            }
        }

        // 如果生成的密码不够，用备用密码填充
        while passwords.count < count {
            let backupPasswords = [
                "Coffee123!@",
                "Brew2024#$",
                "Espresso789&",
                "Latte456*+",
                "Mocha321!?"
            ]
            if let backup = backupPasswords.randomElement() {
                passwords.append(backup)
            }
        }

        return Array(passwords.prefix(count))
    }

    private func selectPassword(_ password: String) {
        self.password = password
        self.confirmPassword = password
    }

    private func copyPasswordToClipboard(_ password: String) {
        #if canImport(UIKit)
        UIPasteboard.general.string = password
        #endif
        // 这里可以添加复制成功的提示
        print("密码已复制到剪贴板: \(password)")
    }

    private func isValidUsername(_ username: String) -> Bool {
        // 检查长度（至少5个字符）
        guard username.count >= 5 else { return false }

        // 检查字符（只允许字母和数字）
        let allowedCharacters = CharacterSet.alphanumerics
        let usernameCharacterSet = CharacterSet(charactersIn: username)
        guard allowedCharacters.isSuperset(of: usernameCharacterSet) else { return false }

        // 检查是否以字母开头
        guard let firstChar = username.first, firstChar.isLetter else { return false }

        return true
    }

    private func isValidEmail(_ email: String) -> Bool {
        let emailRegex = "[A-Z0-9a-z._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,64}"
        let emailPredicate = NSPredicate(format:"SELF MATCHES %@", emailRegex)
        return emailPredicate.evaluate(with: email)
    }
}

#Preview {
    RegisterView()
}


