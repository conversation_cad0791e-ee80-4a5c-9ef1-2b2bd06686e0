# Generated migration for ensuring username uniqueness and performance

from django.db import migrations, models
from django.contrib.auth.models import User

class Migration(migrations.Migration):

    dependencies = [
        ('iosapp', '0002_iosdevice_app_version_iosdevice_blacklisted_at_and_more'),
    ]

    # 禁用原子性，允许CREATE INDEX CONCURRENTLY
    atomic = False

    operations = [
        # 确保用户名字段有唯一索引（Django默认User模型已有，但为了确保性能添加额外索引）
        migrations.RunSQL(
            # 创建用户名的大小写不敏感索引（用于快速查找）
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS auth_user_username_lower_idx ON auth_user (LOWER(username));",
            # 回滚时删除索引
            "DROP INDEX IF EXISTS auth_user_username_lower_idx;",
        ),
        
        # 创建用户名长度检查约束（使用DO块处理IF NOT EXISTS）
        migrations.RunSQL(
            """
            DO $$
            BEGIN
                IF NOT EXISTS (
                    SELECT 1 FROM information_schema.table_constraints
                    WHERE constraint_name = 'username_length_check'
                    AND table_name = 'auth_user'
                ) THEN
                    ALTER TABLE auth_user ADD CONSTRAINT username_length_check
                    CHECK (LENGTH(username) >= 5 AND LENGTH(username) <= 20);
                END IF;
            END $$;
            """,
            "ALTER TABLE auth_user DROP CONSTRAINT IF EXISTS username_length_check;",
        ),

        # 创建用户名格式检查约束（使用DO块处理IF NOT EXISTS）
        migrations.RunSQL(
            """
            DO $$
            BEGIN
                IF NOT EXISTS (
                    SELECT 1 FROM information_schema.table_constraints
                    WHERE constraint_name = 'username_format_check'
                    AND table_name = 'auth_user'
                ) THEN
                    ALTER TABLE auth_user ADD CONSTRAINT username_format_check
                    CHECK (username ~ '^[a-zA-Z][a-zA-Z0-9]*$');
                END IF;
            END $$;
            """,
            "ALTER TABLE auth_user DROP CONSTRAINT IF EXISTS username_format_check;",
        ),
    ]
